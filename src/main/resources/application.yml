server:
  port: 8080
  ssl:
    key-store-type: PKCS12
    key-store: classpath:ftb.htecshm.com.pfx
    key-store-password: q71qvgsw
spring:
  datasource:
    url: ********************************************************************************************************
    driverClassName: com.mysql.cj.jdbc.Driver
    username: root
    password: ch<PERSON><PERSON>@mysql
  jpa:
    show_sql: true
    hibernate:
      ddl-auto: none
    properties:
      hibernate:
#        format_sql: true
        jdbc:
          time_zone: UTC+8
  data:
    redis:
      host: *************
      port: 6379
      password: <PERSON><PERSON>@redis
      database: 15
      lettuce:
        pool:
          max-idle: 16
          max-active: 32
          min-idle: 8
app:
  audit:
    undo-minutes: 10

  wechat:
    appid: wx6345a9f16e6e706d
    secret: f3b19d1c4a08e57d5dba6ac38cefd8a4
    mock-mode: false
  jwt:
    secret: change-this-secret-please
    expire-hours: 168
  room:
    max-players: 12
    idle-auto-dispose-hours: 12

