package com.example.mahjong.repo;

import com.example.mahjong.model.Room;
import com.example.mahjong.model.ScoreTransaction;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;

public interface ScoreTransactionRepository extends JpaRepository<ScoreTransaction, Long> {
    List<ScoreTransaction> findByRoomOrderByCreatedAtDesc(Room room);
    Page<ScoreTransaction> findByRoomOrderByCreatedAtDesc(Room room, Pageable pageable);
    List<ScoreTransaction> findByFromOpenIdOrToOpenIdOrderByCreatedAtDesc(String fromOpenId, String toOpenId);
    Page<ScoreTransaction> findByFromOpenIdOrToOpenIdOrderByCreatedAtDesc(String fromOpenId, String toOpenId, Pageable pageable);
}