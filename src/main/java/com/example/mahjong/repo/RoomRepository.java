package com.example.mahjong.repo;

import com.example.mahjong.model.Room;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.time.Instant;
import java.util.Map;
import java.util.Optional;

public interface RoomRepository extends JpaRepository<Room, Long> {
    Optional<Room> findByCode(String code);
    long deleteByLastActiveAtBefore(Instant expireBefore);

    @Query(value = "select t1.* from rooms t1 inner join room_members t2 on t1.id = t2.room_id where t2.open_id = ?1 order by t1.last_active_at desc limit 1", nativeQuery = true)
    Map<String, Object> getCurrentRoomByOpenId(String openId);
}

