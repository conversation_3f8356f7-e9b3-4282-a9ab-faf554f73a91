package com.example.mahjong.repo;

import com.example.mahjong.model.Room;
import com.example.mahjong.model.RoomMember;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;
import java.util.Optional;

public interface RoomMemberRepository extends JpaRepository<RoomMember, Long> {
    List<RoomMember> findByRoom(Room room);
    Optional<RoomMember> findByRoomAndOpenId(Room room, String openId);
    void deleteByRoomAndOpenId(Room room, String openId);

    // For auto-leave other rooms
    List<RoomMember> findByOpenId(String openId);
    void deleteByOpenId(String openId);

    long countByRoom(Room room);
}

