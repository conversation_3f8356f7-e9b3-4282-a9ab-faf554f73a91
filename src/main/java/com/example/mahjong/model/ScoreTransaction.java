package com.example.mahjong.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.*;
import java.time.Instant;

@Entity
@Table(name = "score_transactions")
@JsonIgnoreProperties({"hibernateLazyInitializer", "handler"})
public class ScoreTransaction {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "room_id")
    @JsonIgnoreProperties({"hibernateLazyInitializer", "handler", "members"})
    private Room room;

    private String fromOpenId;
    private String toOpenId;

    private String fromName;
    private String toName;

    private int amount; // > 0

    private Instant createdAt = Instant.now();

    public Long getId() { return id; }
    public Room getRoom() { return room; }
    public void setRoom(Room room) { this.room = room; }
    public String getFromOpenId() { return fromOpenId; }
    public void setFromOpenId(String fromOpenId) { this.fromOpenId = fromOpenId; }
    public String getToOpenId() { return toOpenId; }
    public void setToOpenId(String toOpenId) { this.toOpenId = toOpenId; }
    public String getFromName() { return fromName; }
    public void setFromName(String fromName) { this.fromName = fromName; }
    public String getToName() { return toName; }
    public void setToName(String toName) { this.toName = toName; }
    public int getAmount() { return amount; }
    public void setAmount(int amount) { this.amount = amount; }
    public Instant getCreatedAt() { return createdAt; }
}