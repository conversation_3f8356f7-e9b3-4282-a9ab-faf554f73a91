package com.example.mahjong.service;

import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import io.jsonwebtoken.security.Keys;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.crypto.SecretKey;
import java.nio.charset.StandardCharsets;
import java.util.Date;

@Service
public class JwtService {
    @Value("${app.jwt.secret}")
    private String secret;

    @Value("${app.jwt.expire-hours}")
    private int expireHours;

    public static SecretKey deriveKey(String secret){
        byte[] raw = secret.getBytes(StandardCharsets.UTF_8);
        if (raw.length < 32){
            byte[] padded = new byte[32];
            System.arraycopy(raw, 0, padded, 0, Math.min(raw.length, 32));
            raw = padded;
        }
        return Keys.hmacShaKeyFor(raw);
    }

    public String createToken(String openId) {
        long now = System.currentTimeMillis();
        return Jwts.builder()
                .claim("openId", openId)
                .setIssuedAt(new Date(now))
                .setExpiration(new Date(now + expireHours * 3600_000L))
                .signWith(deriveKey(secret), SignatureAlgorithm.HS256)
                .compact();
    }
}

