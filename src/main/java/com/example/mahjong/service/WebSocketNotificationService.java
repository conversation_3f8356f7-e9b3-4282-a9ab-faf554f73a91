package com.example.mahjong.service;

import com.example.mahjong.websocket.WebSocketMessage;
import com.example.mahjong.websocket.WebSocketSessionManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

@Service
public class WebSocketNotificationService {
    
    private static final Logger log = LoggerFactory.getLogger(WebSocketNotificationService.class);
    
    private final WebSocketSessionManager sessionManager;
    private final RoomService roomService;
    
    public WebSocketNotificationService(WebSocketSessionManager sessionManager, RoomService roomService) {
        this.sessionManager = sessionManager;
        this.roomService = roomService;
    }
    
    /**
     * 通知房间数据更新
     */
    public void notifyRoomUpdate(String roomCode) {
        try {
            // 获取房间完整信息
            Map<String, Object> roomData = getRoomFullData(roomCode);
            if (roomData != null) {
                WebSocketMessage message = WebSocketMessage.roomUpdate(roomCode, roomData);
                sessionManager.sendToRoom(roomCode, message);
                log.debug("已通知房间 {} 数据更新", roomCode);
            }
        } catch (Exception e) {
            log.error("通知房间更新失败: {}", e.getMessage());
        }
    }
    
    /**
     * 通知成员加入
     */
    public void notifyMemberJoin(String roomCode, String openId, String displayName) {
        try {
            Map<String, Object> memberData = new HashMap<>();
            memberData.put("openId", openId);
            memberData.put("displayName", displayName);
            memberData.put("action", "join");
            
            WebSocketMessage message = WebSocketMessage.memberJoin(roomCode, memberData);
            sessionManager.sendToRoom(roomCode, message);
            
            // 同时发送完整房间数据更新
            notifyRoomUpdate(roomCode);
            
            log.info("已通知房间 {} 成员 {} 加入", roomCode, displayName);
        } catch (Exception e) {
            log.error("通知成员加入失败: {}", e.getMessage());
        }
    }
    
    /**
     * 通知成员离开
     */
    public void notifyMemberLeave(String roomCode, String openId, String displayName) {
        try {
            Map<String, Object> memberData = new HashMap<>();
            memberData.put("openId", openId);
            memberData.put("displayName", displayName);
            memberData.put("action", "leave");
            
            WebSocketMessage message = WebSocketMessage.memberLeave(roomCode, memberData);
            sessionManager.sendToRoom(roomCode, message);
            
            // 同时发送完整房间数据更新
            notifyRoomUpdate(roomCode);
            
            log.info("已通知房间 {} 成员 {} 离开", roomCode, displayName);
        } catch (Exception e) {
            log.error("通知成员离开失败: {}", e.getMessage());
        }
    }
    
    /**
     * 通知积分转账
     */
    public void notifyScoreTransfer(String roomCode, String fromOpenId, String toOpenId, int amount) {
        try {
            Map<String, Object> transferData = new HashMap<>();
            transferData.put("fromOpenId", fromOpenId);
            transferData.put("toOpenId", toOpenId);
            transferData.put("amount", amount);
            transferData.put("timestamp", System.currentTimeMillis());
            
            WebSocketMessage message = WebSocketMessage.scoreTransfer(roomCode, transferData);
            sessionManager.sendToRoom(roomCode, message);
            
            // 同时发送完整房间数据更新
            notifyRoomUpdate(roomCode);
            
            log.info("已通知房间 {} 积分转账: {} -> {}, 金额: {}", roomCode, fromOpenId, toOpenId, amount);
        } catch (Exception e) {
            log.error("通知积分转账失败: {}", e.getMessage());
        }
    }
    
    /**
     * 通知房间解散
     */
    public void notifyRoomDissolved(String roomCode) {
        try {
            WebSocketMessage message = WebSocketMessage.roomDissolved(roomCode);
            sessionManager.sendToRoom(roomCode, message);
            
            log.info("已通知房间 {} 解散", roomCode);
        } catch (Exception e) {
            log.error("通知房间解散失败: {}", e.getMessage());
        }
    }
    
    /**
     * 通知成员被踢出
     */
    public void notifyMemberKicked(String roomCode, String kickedOpenId, String kickedDisplayName) {
        try {
            Map<String, Object> kickData = new HashMap<>();
            kickData.put("kickedOpenId", kickedOpenId);
            kickData.put("kickedDisplayName", kickedDisplayName);
            
            WebSocketMessage message = WebSocketMessage.memberKicked(roomCode, kickedDisplayName);
            sessionManager.sendToRoom(roomCode, message);
            
            // 同时发送完整房间数据更新
            notifyRoomUpdate(roomCode);
            
            log.info("已通知房间 {} 成员 {} 被踢出", roomCode, kickedDisplayName);
        } catch (Exception e) {
            log.error("通知成员被踢出失败: {}", e.getMessage());
        }
    }
    
    /**
     * 获取房间完整数据
     */
    private Map<String, Object> getRoomFullData(String roomCode) {
        try {
            // 使用现有的roomInfo接口逻辑
            Map<String, Object> result = new HashMap<>();
            
            // 房间信息
            var room = roomService.findByCode(roomCode);
            if (room == null) {
                return null;
            }
            
            Map<String, Object> roomInfo = new HashMap<>();
            roomInfo.put("code", roomCode);
            roomInfo.put("ownerOpenId", room.getOwnerOpenId());
            result.put("room", roomInfo);
            
            // 成员信息
            result.put("members", roomService.getMembers(roomCode));
            
            // 交易记录
            result.put("transactions", roomService.getRoomTransactions(roomCode));
            
            return result;
        } catch (Exception e) {
            log.error("获取房间完整数据失败: {}", e.getMessage());
            return null;
        }
    }
    
    /**
     * 获取房间连接数
     */
    public int getRoomConnectionCount(String roomCode) {
        return sessionManager.getRoomConnectionCount(roomCode);
    }
    
    /**
     * 获取总连接数
     */
    public int getTotalConnectionCount() {
        return sessionManager.getTotalConnectionCount();
    }
}
