package com.example.mahjong.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

@Service
public class WeChatAuthService {
    @Value("${app.wechat.appid}")
    private String appid;
    @Value("${app.wechat.secret}")
    private String secret;
    @Value("${app.wechat.mock-mode}")
    private boolean mockMode;

    private final ObjectMapper mapper = new ObjectMapper();

    public String getOpenIdByJsCode(String jsCode) {
        if (mockMode) {
            return "mock_openid_" + Math.abs(jsCode.hashCode());
        }
        String url = String.format("https://api.weixin.qq.com/sns/jscode2session?appid=%s&secret=%s&js_code=%s&grant_type=authorization_code", appid, secret, jsCode);
        RestTemplate restTemplate = new RestTemplate();
        ResponseEntity<String> resp = restTemplate.getForEntity(url, String.class);
        try {
            JsonNode node = mapper.readTree(resp.getBody());
            if (node.has("openid")) {
                return node.get("openid").asText();
            } else {
                throw new RuntimeException("WeChat auth failed: " + resp.getBody());
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}

