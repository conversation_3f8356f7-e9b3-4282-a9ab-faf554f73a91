package com.example.mahjong.service;

import com.example.mahjong.model.Room;
import com.example.mahjong.model.RoomMember;
import com.example.mahjong.model.ScoreTransaction;
import com.example.mahjong.model.User;
import com.example.mahjong.repo.RoomMemberRepository;
import com.example.mahjong.repo.RoomRepository;
import com.example.mahjong.repo.ScoreTransactionRepository;
import lombok.Setter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.security.SecureRandom;
import java.time.Instant;
import java.util.*;

/**
 * <AUTHOR>
 */
@Service
public class RoomService {

    private static final Logger log = LoggerFactory.getLogger(RoomService.class);
    private final RoomRepository roomRepository;
    private final RoomMemberRepository memberRepository;
    private final ScoreTransactionRepository txRepository;
    private final RedisTemplate<String, Object> redisTemplate;
    // 设置WebSocket通知服务（避免循环依赖）
    @Setter
    private WebSocketNotificationService webSocketNotificationService;

    @Value("${app.room.max-players}")
    private int maxPlayers;

    public RoomService(RedisTemplate<String, Object> redisTemplate,
            ScoreTransactionRepository txRepository,
            RoomMemberRepository memberRepository,
            RoomRepository roomRepository) {
        this.redisTemplate = redisTemplate;
        this.txRepository = txRepository;
        this.memberRepository = memberRepository;
        this.roomRepository = roomRepository;
    }

    private synchronized String generateRoomCode() {
        SecureRandom rnd = new SecureRandom();
        while (true) {
            int num = 100000 + rnd.nextInt(900000);
            String code = String.valueOf(num);
            if (roomRepository.findByCode(code).isEmpty()) {
                log.info("Generated room code: {}", code);
                return code;
            }
        }
    }

    @Transactional
    public Room createRoom(String ownerOpenId) {
        Room room = new Room();
        room.setCode(generateRoomCode());
        room.setOwnerOpenId(ownerOpenId);
        roomRepository.save(room);
        // owner joins as member
        Object o = redisTemplate.opsForHash().get("user", ownerOpenId);
        User user = (User) o;
        joinRoom(room.getCode(), ownerOpenId, user.getDisplayName(), user.getAvatarUrl());
        return room;
    }

    @Transactional
    public Room joinRoom(String code, String openId, String displayName, String avatarUrl) {
        Room room = roomRepository.findByCode(code).orElseThrow(() -> new RuntimeException("Room not found"));
        List<RoomMember> members = memberRepository.findByRoom(room);
        Optional<RoomMember> existing = memberRepository.findByRoomAndOpenId(room, openId);
        if (existing.isPresent()) {
            // 如果用户已经是成员，保留其现有的显示名称和余额，只更新头像
            RoomMember m = existing.get();
            m.setAvatarUrl(avatarUrl);
            memberRepository.save(m);
            return room;
        }
        if (members.size() >= maxPlayers) {
            throw new RuntimeException("Room is full");
        }
        // auto leave other rooms before joining
        memberRepository.deleteByOpenId(openId);
        RoomMember m = new RoomMember();
        m.setRoom(room);
        m.setOpenId(openId);
        m.setDisplayName(displayName != null ? displayName : ("玩家" + openId.substring(Math.max(0, openId.length() - 4))));
        m.setAvatarUrl(avatarUrl);
        m.setBalance(0);
        memberRepository.save(m);
        room.touch();

        log.info("玩家: {}, 进入房间: {}", openId, code);
        Room savedRoom = roomRepository.save(room);

        // 发送WebSocket通知
        if (webSocketNotificationService != null) {
            // 只有新加入的用户才发送通知，重新进入的用户不发送
            webSocketNotificationService.notifyMemberJoin(code, openId, m.getDisplayName());
        }

        return savedRoom;
    }

    @Transactional
    public void leaveRoom(String code, String openId) {
        Room room = roomRepository.findByCode(code).orElseThrow(() -> new RuntimeException("Room not found"));

        // 获取离开用户的信息用于通知
        Optional<RoomMember> leavingMember = memberRepository.findByRoomAndOpenId(room, openId);
        String displayName = leavingMember.map(RoomMember::getDisplayName).orElse("未知用户");

        memberRepository.deleteByRoomAndOpenId(room, openId);

        log.info("玩家: {}, 离开房间: {}", openId, code);
        // 检查是否还有其他成员
        long remainingMembers = memberRepository.countByRoom(room);
        log.info("remainingMembers: {}", remainingMembers);
        if (remainingMembers == 0) {
            // 最后一名玩家退出，销毁房间
            if (webSocketNotificationService != null) {
                webSocketNotificationService.notifyRoomDissolved(code);
            }
            roomRepository.delete(room);
        } else {
            // 如果离开的是房主，需要转移房主权限
            if (room.getOwnerOpenId().equals(openId)) {
                List<RoomMember> members = memberRepository.findByRoom(room);
                if (!members.isEmpty()) {
                    // 选择ID最小的成员作为新房主（即最早加入房间的成员）
                    RoomMember newOwner = members.stream().min(Comparator.comparing(RoomMember::getId)).orElse(members.get(0));
                    room.setOwnerOpenId(newOwner.getOpenId());
                }
            }
            room.touch();
            roomRepository.save(room);

            // 发送WebSocket通知
            if (webSocketNotificationService != null) {
                webSocketNotificationService.notifyMemberLeave(code, openId, displayName);
            }
        }
    }

    @Transactional
    public void kickMember(String code, String requesterOpenId, String targetOpenId) {
        Room room = roomRepository.findByCode(code).orElseThrow(() -> new RuntimeException("Room not found"));
        if (!requesterOpenId.equals(room.getOwnerOpenId())) {
            throw new RuntimeException("Only owner can kick");
        }

        // 获取被踢用户的信息用于通知
        Optional<RoomMember> kickedMember = memberRepository.findByRoomAndOpenId(room, targetOpenId);
        String displayName = kickedMember.map(RoomMember::getDisplayName).orElse("未知用户");

        memberRepository.deleteByRoomAndOpenId(room, targetOpenId);
        room.touch();
        roomRepository.save(room);

        // 发送WebSocket通知
        if (webSocketNotificationService != null) {
            webSocketNotificationService.notifyMemberKicked(code, targetOpenId, displayName);
        }
    }

    @Transactional
    public void dissolveRoom(String code, String requesterOpenId) {
        Room room = roomRepository.findByCode(code).orElseThrow(() -> new RuntimeException("Room not found"));
        if (!requesterOpenId.equals(room.getOwnerOpenId())) {
            throw new RuntimeException("Only owner can dissolve");
        }

        // 发送WebSocket通知（在删除房间之前）
        if (webSocketNotificationService != null) {
            webSocketNotificationService.notifyRoomDissolved(code);
        }

        roomRepository.delete(room);
        log.info("Room dissolved: {}", code);
    }

    @Transactional
    public void updateDisplayName(String code, String openId, String displayName) {
        Room room = roomRepository.findByCode(code).orElseThrow(() -> new RuntimeException("Room not found"));
        RoomMember m = memberRepository.findByRoomAndOpenId(room, openId).orElseThrow(() -> new RuntimeException("Not a member"));
        m.setDisplayName(displayName);
        memberRepository.save(m);
        room.touch();
        roomRepository.save(room);
        // 更新redis中的用户信息
        Object o = redisTemplate.opsForHash().get("user", openId);
        User user = (User) o;
        user.setDisplayName(displayName);
        redisTemplate.opsForHash().put("user", openId, user);

        // 发送WebSocket通知
        if (webSocketNotificationService != null) {
            webSocketNotificationService.notifyRoomUpdate(code);
        }
    }

    @Transactional
    public void transferScore(String code, String fromOpenId, String toOpenId, int amount) {
        if (amount <= 0) {
            throw new RuntimeException("Amount must be > 0");
        }
        Room room = getRoomByCode(code);
        RoomMember from = getRoomMember(room, fromOpenId).orElseThrow(() -> new RuntimeException("Sender not in room"));
        RoomMember to = getRoomMember(room, toOpenId).orElseThrow(() -> new RuntimeException("Receiver not in room"));

        from.setBalance(from.getBalance() - amount);
        to.setBalance(to.getBalance() + amount);
        memberRepository.save(from);
        memberRepository.save(to);
        ScoreTransaction tx = new ScoreTransaction();
        tx.setRoom(room);
        tx.setFromOpenId(fromOpenId);
        tx.setToOpenId(toOpenId);
        tx.setFromName(from.getDisplayName());
        tx.setToName(to.getDisplayName());
        tx.setAmount(amount);
        txRepository.save(tx);
        room.touch();
        roomRepository.save(room);

        // 发送WebSocket通知
        if (webSocketNotificationService != null) {
            webSocketNotificationService.notifyScoreTransfer(code, fromOpenId, toOpenId, amount);
        }
    }

    public List<RoomMember> getMembers(String code) {
        Room room = roomRepository.findByCode(code).orElse(null);
        if (room == null) {
            return Collections.emptyList();
        }
        return memberRepository.findByRoom(room);
    }

    @Transactional(readOnly = true)
    public List<ScoreTransaction> getRoomTransactions(String code) {
        return getRoomTransactions(code, 0, 50); // 默认返回前50条记录
    }

    @Transactional(readOnly = true)
    public List<ScoreTransaction> getRoomTransactions(String code, int page, int size) {
        Room room = roomRepository.findByCode(code).orElse(null);

        if (room == null) {
            return Collections.emptyList();
        }

        if (page < 0 || size < 1) {
            throw new IllegalArgumentException("Page and size must be positive");
        }

        Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "createdAt"));

        List<ScoreTransaction> content = txRepository.findByRoomOrderByCreatedAtDesc(room, pageable).getContent();
        // 从redis中获取用户列表， 并替换人员显示名称
        Map<Object, Object> objectMap = redisTemplate.opsForHash().entries("user");
        content.forEach(item -> {
            Object o = objectMap.get(item.getFromOpenId());
            if(o != null) {
                User user = (User) o;
                item.setFromName(user.getDisplayName());
            }
            o = objectMap.get(item.getToOpenId());
            if(o != null) {
                User user = (User) o;
                item.setToName(user.getDisplayName());
            }
        });
        return content;
    }

    public List<ScoreTransaction> getUserHistory(String openId) {
        return getUserHistory(openId, 0, 50); // 默认返回前50条记录
    }

    public List<ScoreTransaction> getUserHistory(String openId, int page, int size) {
        Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "createdAt"));
        return txRepository.findByFromOpenIdOrToOpenIdOrderByCreatedAtDesc(openId, openId, pageable).getContent();
    }

    @Transactional
    public long cleanupIdleRooms(long hours) {
        Instant expireBefore = Instant.now().minusSeconds(hours * 3600);
        return roomRepository.deleteByLastActiveAtBefore(expireBefore);
    }

    public Room findByCode(String code) {
        return roomRepository.findByCode(code).orElse(null);
    }

    /**
     * 根据房间编码获取房间对象
     *
     * @param code 房间编码
     * @return 房间对象
     */
    private Room getRoomByCode(String code) {
        return roomRepository.findByCode(code).orElseThrow(() -> new RuntimeException("Room not found"));
    }

    /**
     * 获取房间中的成员
     *
     * @param room   房间对象
     * @param openId 用户的OpenId
     * @return 房间中的成员
     */
    private Optional<RoomMember> getRoomMember(Room room, String openId) {
        return memberRepository.findByRoomAndOpenId(room, openId);
    }

    public Map<String, Object> getCurrentRoom(String openId) {
        return roomRepository.getCurrentRoomByOpenId(openId);
    }

    /**
     * 检查用户是否在指定房间中
     */
    public boolean isUserInRoom(String openId, String roomCode) {
        Room room = roomRepository.findByCode(roomCode).orElse(null);
        if (room == null) {
            return false;
        }
        return memberRepository.findByRoomAndOpenId(room, openId).isPresent();
    }
}