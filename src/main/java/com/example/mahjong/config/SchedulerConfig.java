package com.example.mahjong.config;

import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.example.mahjong.service.RoomService;
import com.example.mahjong.websocket.WebSocketSessionManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;

@Configuration
@EnableScheduling
public class SchedulerConfig {

    private static final Logger log = LoggerFactory.getLogger(SchedulerConfig.class);

    private final RoomService roomService;

    private final RedisTemplate<String, Object> redisTemplate;

    private final WebSocketSessionManager webSocketSessionManager;

    @Value("${app.room.idle-auto-dispose-hours}")
    private long idleHours;

    @Value("${app.wechat.appid}")
    private String appid;

    @Value("${app.wechat.secret}")
    private String secret;

    public SchedulerConfig(RoomService roomService,
                          RedisTemplate<String, Object> redisTemplate,
                          WebSocketSessionManager webSocketSessionManager) {
        this.roomService = roomService;
        this.redisTemplate = redisTemplate;
        this.webSocketSessionManager = webSocketSessionManager;
    }

    @Scheduled(fixedDelay = 3600_000)
    public void cleanup() {
        roomService.cleanupIdleRooms(idleHours);
    }

    // 每5000秒获取一次微信access_token
    @Scheduled(fixedDelay = 5000_000)
    public void getWeChatAccessToken() {
        String url = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=" + appid + "&secret=" + secret;
        String s = HttpUtil.get(url);
        log.info("getWeChatAccessToken: {}", s);
        JSONObject response = JSONUtil.parseObj(s);
        redisTemplate.opsForValue().set("wechat:access_token", response.getStr("access_token"));
    }

    // 每30秒清理超时的WebSocket连接
    @Scheduled(fixedDelay = 30_000)
    public void cleanupWebSocketSessions() {
        // 清理超过5分钟没有心跳的连接
        webSocketSessionManager.cleanupTimeoutSessions(5 * 60 * 1000);
    }
}