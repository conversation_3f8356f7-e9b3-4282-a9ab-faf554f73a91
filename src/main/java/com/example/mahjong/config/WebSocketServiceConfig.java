package com.example.mahjong.config;

import com.example.mahjong.service.RoomService;
import com.example.mahjong.service.WebSocketNotificationService;
import org.springframework.context.annotation.Configuration;

import jakarta.annotation.PostConstruct;

@Configuration
public class WebSocketServiceConfig {
    
    private final RoomService roomService;
    private final WebSocketNotificationService webSocketNotificationService;
    
    public WebSocketServiceConfig(RoomService roomService, 
                                 WebSocketNotificationService webSocketNotificationService) {
        this.roomService = roomService;
        this.webSocketNotificationService = webSocketNotificationService;
    }
    
    @PostConstruct
    public void init() {
        // 解决循环依赖问题
        roomService.setWebSocketNotificationService(webSocketNotificationService);
    }
}
