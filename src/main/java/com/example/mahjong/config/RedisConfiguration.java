package com.example.mahjong.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.RedisSerializer;

/**
 * @ClassName RedisConfiguration  //类名称
 * @Description: 类描述
 * @Author: pcd    //作者
 * @CreateDate: 2025-08-28 09:47:46 09:47	//创建时间
 * @UpdateUser: 更新人
 * @UpdateDate: 2025-08-28 09:47:46 09:47	//更新时间
 * @UpdateRemark: 更新的信息
 * @Version: 1.0    //版本号
 */

@Configuration
public class RedisConfiguration {

    @Bean
    public RedisTemplate redisTemplate(RedisConnectionFactory redisConnectionFactory){
        RedisTemplate redisTemplate = new RedisTemplate();
        // 设置redis的连接工厂对象
        // 在这步可以切换 redis 客户端，前提是定义好 redisConnectionFactory
        redisTemplate.setConnectionFactory(redisConnectionFactory);

        // 设置redis key的序列化器
        redisTemplate.setKeySerializer(RedisSerializer.string());
        redisTemplate.setHashKeySerializer(RedisSerializer.string());
        // 设置redis value的序列化器
        // 自动添加 `@class` 类型信息，避免反序列化失败
        // 特征：序列化后有 class 信息
        redisTemplate.setValueSerializer(RedisSerializer.json());
        redisTemplate.setHashValueSerializer(RedisSerializer.json());
        return redisTemplate;
    }
}
