package com.example.mahjong.websocket;

import cn.hutool.json.JSONUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;

import java.io.IOException;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArraySet;

/**
 * <AUTHOR>
 */
@Component
public class WebSocketSessionManager {
    
    private static final Logger log = LoggerFactory.getLogger(WebSocketSessionManager.class);
    
    // 房间 -> 会话集合
    private final Map<String, Set<WebSocketSession>> roomSessions = new ConcurrentHashMap<>();
    
    // 会话 -> 用户信息
    private final Map<WebSocketSession, UserSessionInfo> sessionUsers = new ConcurrentHashMap<>();
    
    /**
     * 用户会话信息
     */
    public static class UserSessionInfo {
        private String openId;
        private String roomCode;
        private long lastHeartbeat;
        
        public UserSessionInfo(String openId, String roomCode) {
            this.openId = openId;
            this.roomCode = roomCode;
            this.lastHeartbeat = System.currentTimeMillis();
        }
        
        // getters and setters
        public String getOpenId() { return openId; }
        public void setOpenId(String openId) { this.openId = openId; }
        public String getRoomCode() { return roomCode; }
        public void setRoomCode(String roomCode) { this.roomCode = roomCode; }
        public long getLastHeartbeat() { return lastHeartbeat; }
        public void setLastHeartbeat(long lastHeartbeat) { this.lastHeartbeat = lastHeartbeat; }
    }
    
    /**
     * 添加会话到房间
     */
    public void addSessionToRoom(WebSocketSession session, String roomCode, String openId) {
        // 移除旧的房间关联
        removeSession(session);
        
        // 添加到新房间
        roomSessions.computeIfAbsent(roomCode, k -> new CopyOnWriteArraySet<>()).add(session);
        sessionUsers.put(session, new UserSessionInfo(openId, roomCode));
        
        log.info("用户 {} 连接到房间 {}, 当前房间连接数: {}", openId, roomCode, roomSessions.get(roomCode).size());
    }
    
    /**
     * 移除会话
     */
    public void removeSession(WebSocketSession session) {
        UserSessionInfo userInfo = sessionUsers.remove(session);
        if (userInfo != null) {
            String roomCode = userInfo.getRoomCode();
            Set<WebSocketSession> sessions = roomSessions.get(roomCode);
            if (sessions != null) {
                sessions.remove(session);
                if (sessions.isEmpty()) {
                    roomSessions.remove(roomCode);
                }
                log.info("用户 {} 断开房间 {} 连接, 当前房间连接数: {}", 
                        userInfo.getOpenId(), roomCode, sessions.size());
            }
        }
    }
    
    /**
     * 更新心跳时间
     */
    public void updateHeartbeat(WebSocketSession session) {
        UserSessionInfo userInfo = sessionUsers.get(session);
        if (userInfo != null) {
            userInfo.setLastHeartbeat(System.currentTimeMillis());
        }
    }
    
    /**
     * 获取用户信息
     */
    public UserSessionInfo getUserInfo(WebSocketSession session) {
        return sessionUsers.get(session);
    }
    
    /**
     * 向房间内所有用户发送消息
     */
    public void sendToRoom(String roomCode, WebSocketMessage message) {
        Set<WebSocketSession> sessions = roomSessions.get(roomCode);
        if (sessions == null || sessions.isEmpty()) {
            return;
        }
        
        String messageText = JSONUtil.toJsonStr(message);
        TextMessage textMessage = new TextMessage(messageText);
        
        // 使用副本避免并发修改异常
        Set<WebSocketSession> sessionsCopy = new CopyOnWriteArraySet<>(sessions);
        
        for (WebSocketSession session : sessionsCopy) {
            try {
                if (session.isOpen()) {
                    session.sendMessage(textMessage);
                } else {
                    // 清理已关闭的会话
                    removeSession(session);
                }
            } catch (IOException e) {
                log.error("发送消息到会话失败: {}", e.getMessage());
                // 清理出错的会话
                removeSession(session);
            }
        }
        
        log.debug("向房间 {} 发送消息: {}, 接收者数量: {}", roomCode, message.getType(), sessionsCopy.size());
    }

    /**
     * 向特定用户发送消息
     */
    public void sendToUser(WebSocketSession session, WebSocketMessage message) {
        if (session == null || !session.isOpen()) {
            return;
        }

        try {
            String messageText = JSONUtil.toJsonStr(message);
            session.sendMessage(new TextMessage(messageText));
        } catch (IOException e) {
            log.error("发送消息到用户失败: {}", e.getMessage());
            removeSession(session);
        }
    }

    /**
     * 获取房间连接数
     */
    public int getRoomConnectionCount(String roomCode) {
        Set<WebSocketSession> sessions = roomSessions.get(roomCode);
        return sessions != null ? sessions.size() : 0;
    }

    /**
     * 获取总连接数
     */
    public int getTotalConnectionCount() {
        return sessionUsers.size();
    }

    /**
     * 清理超时连接
     */
    public void cleanupTimeoutSessions(long timeoutMs) {
        long currentTime = System.currentTimeMillis();
        sessionUsers.entrySet().removeIf(entry -> {
            UserSessionInfo userInfo = entry.getValue();
            if (currentTime - userInfo.getLastHeartbeat() > timeoutMs) {
                WebSocketSession session = entry.getKey();
                try {
                    if (session.isOpen()) {
                        session.close();
                    }
                } catch (IOException e) {
                    log.error("关闭超时会话失败: {}", e.getMessage());
                }
                removeSession(session);
                return true;
            }
            return false;
        });
    }
}
