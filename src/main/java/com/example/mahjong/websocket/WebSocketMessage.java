package com.example.mahjong.websocket;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.time.Instant;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class WebSocketMessage {
    
    public enum MessageType {
        // 房间数据更新
        ROOM_UPDATE,
        // 成员加入
        MEMBER_JOIN,
        // 成员离开
        MEMBER_LEAVE,
        // 积分转账
        SCORE_TRANSFER,
        // 房间解散
        ROOM_DISSOLVED,
        // 成员被踢出
        MEMBER_KICKED,
        // 心跳
        HEARTBEAT,
        // 连接确认
        CONNECTION_ACK,
        // 错误消息
        ERROR
    }
    
    private MessageType type;
    private String roomCode;
    private Object data;
    private String message;
    private Long timestamp;
    
    public WebSocketMessage() {
        this.timestamp = Instant.now().toEpochMilli();
    }
    
    public WebSocketMessage(MessageType type, String roomCode, Object data) {
        this();
        this.type = type;
        this.roomCode = roomCode;
        this.data = data;
    }
    
    public WebSocketMessage(MessageType type, String roomCode, String message) {
        this();
        this.type = type;
        this.roomCode = roomCode;
        this.message = message;
    }
    
    public static WebSocketMessage roomUpdate(String roomCode, Object data) {
        return new WebSocketMessage(MessageType.ROOM_UPDATE, roomCode, data);
    }
    
    public static WebSocketMessage memberJoin(String roomCode, Object memberData) {
        return new WebSocketMessage(MessageType.MEMBER_JOIN, roomCode, memberData);
    }
    
    public static WebSocketMessage memberLeave(String roomCode, Object memberData) {
        return new WebSocketMessage(MessageType.MEMBER_LEAVE, roomCode, memberData);
    }
    
    public static WebSocketMessage scoreTransfer(String roomCode, Object transactionData) {
        return new WebSocketMessage(MessageType.SCORE_TRANSFER, roomCode, transactionData);
    }
    
    public static WebSocketMessage roomDissolved(String roomCode) {
        return new WebSocketMessage(MessageType.ROOM_DISSOLVED, roomCode, "房间已解散");
    }
    
    public static WebSocketMessage memberKicked(String roomCode, String kickedMember) {
        return new WebSocketMessage(MessageType.MEMBER_KICKED, roomCode, kickedMember + " 被踢出房间");
    }
    
    public static WebSocketMessage heartbeat() {
        return new WebSocketMessage(MessageType.HEARTBEAT, null, "heartbeat");
    }
    
    public static WebSocketMessage connectionAck(String roomCode) {
        return new WebSocketMessage(MessageType.CONNECTION_ACK, roomCode, "连接成功");
    }
    
    public static WebSocketMessage error(String message) {
        return new WebSocketMessage(MessageType.ERROR, null, message);
    }
}
