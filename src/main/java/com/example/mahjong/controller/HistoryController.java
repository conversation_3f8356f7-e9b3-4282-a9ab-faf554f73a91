package com.example.mahjong.controller;

import com.example.mahjong.model.ScoreTransaction;
import com.example.mahjong.security.AuthInterceptor;
import com.example.mahjong.service.RoomService;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/api/history")
public class HistoryController {

    private final RoomService roomService;

    public HistoryController(RoomService roomService) {
        this.roomService = roomService;
    }

    private String getOpenId(HttpServletRequest request) {
        return (String) request.getAttribute(AuthInterceptor.ATTR_OPENID);
    }

    @GetMapping("")
    public List<ScoreTransaction> history(HttpServletRequest request) {
        return roomService.getUserHistory(getOpenId(request));
    }
}

