package com.example.mahjong.controller;

import cn.hutool.json.JSONUtil;
import com.example.mahjong.model.User;
import com.example.mahjong.service.JwtService;
import com.example.mahjong.service.WeChatAuthService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/api/auth")
public class AuthController {
    private static final Logger log = LoggerFactory.getLogger(AuthController.class);

    private final WeChatAuthService weChatAuthService;
    private final JwtService jwtService;
    private final RedisTemplate<String, Object> redisTemplate;

    public AuthController(WeChatAuthService weChatAuthService, JwtService jwtService, RedisTemplate<String, Object> redisTemplate) {
        this.weChatAuthService = weChatAuthService;
        this.jwtService = jwtService;
        this.redisTemplate = redisTemplate;
    }

    @PostMapping("/login")
    public ResponseEntity<?> login(@RequestBody Map<String, String> body) {
        log.info("login: {}", JSONUtil.toJsonPrettyStr(body));

        String jsCode = body.get("code");
        if (jsCode == null || jsCode.isBlank()) {
            return ResponseEntity.badRequest().body("missing code");
        }
        String openId = weChatAuthService.getOpenIdByJsCode(jsCode);
        String token = jwtService.createToken(openId);
        Map<String, Object> resp = new HashMap<>();
        resp.put("token", token);
        resp.put("openId", openId);

        Object o = redisTemplate.opsForHash().get("user", openId);
        if(o != null) {
            User user = (User) o;
            resp.put("nickName", user.getDisplayName());
        }

        redisTemplate.opsForHash().putIfAbsent("user", openId, new User(body.get("nickName"), body.get("avatarUrl"), openId));
        return ResponseEntity.ok(resp);
    }
}

