package com.example.mahjong.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.json.JSONUtil;
import com.example.mahjong.model.Room;
import com.example.mahjong.model.RoomMember;
import com.example.mahjong.model.ScoreTransaction;
import com.example.mahjong.model.User;
import com.example.mahjong.security.AuthInterceptor;
import com.example.mahjong.service.RoomService;
import com.google.zxing.WriterException;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.*;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/room")
public class RoomController {

    private final RoomService roomService;

    private final RedisTemplate<String, Object> redisTemplate;

    public RoomController(RoomService roomService, RedisTemplate<String, Object> redisTemplate) {
        this.roomService = roomService;
        this.redisTemplate = redisTemplate;
    }

    private String getOpenId(HttpServletRequest request) {
        return (String) request.getAttribute(AuthInterceptor.ATTR_OPENID);
    }

    @PostMapping("/create")
    public ResponseEntity<?> createRoom(HttpServletRequest request) {
        String openId = getOpenId(request);
        Room room = roomService.createRoom(openId);
        Map<String, Object> resp = new HashMap<>();
        resp.put("code", room.getCode());
        return ResponseEntity.ok(resp);
    }

    @GetMapping("/{code}")
    public ResponseEntity<?> info(@PathVariable String code) {
        com.example.mahjong.model.Room room = roomService.findByCode(code);
        if (room == null) {
            return ResponseEntity.ok(new HashMap<>());
        }
        Map<String, Object> m = new HashMap<>();
        m.put("code", code);
        m.put("ownerOpenId", room.getOwnerOpenId());
        return ResponseEntity.ok(m);
    }


    @GetMapping(value = "/{code}/qr", produces = MediaType.IMAGE_PNG_VALUE)
    public @ResponseBody byte[] getRoomQr(@PathVariable String code) throws IOException, WriterException {

        String accessToken = (String) redisTemplate.opsForValue().get("wechat:access_token");
        String url = "https://api.weixin.qq.com/wxa/getwxacodeunlimit?access_token=" + accessToken;

        // 请求体
        Map<String, Object> body = new HashMap<>();
        // 参数
        body.put("scene", "code=" + code);
        // 小程序页面路径
        body.put("page", "pages/room/room");
        // 二维码宽度，默认 430px
        body.put("width", 430);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        HttpEntity<String> entity = new HttpEntity<>(JSONUtil.toJsonStr(body), headers);
        RestTemplate restTemplate = new RestTemplate();
        ResponseEntity<byte[]> response = restTemplate.exchange(url, HttpMethod.POST, entity, byte[].class);
        // 返回结果可能是图片字节流，也可能是错误 JSON
        byte[] result = response.getBody();
        String contentType = response.getHeaders().getContentType().toString();

        if (!contentType.contains("image")) {
            throw new RuntimeException(new String(result));
        }
        // 成功返回二维码
        return result;
    }

    @PostMapping("/join")
    public ResponseEntity<?> join(HttpServletRequest request, @RequestBody Map<String, String> body) {
        String openId = getOpenId(request);
        String code = body.get("code");

        Object o = redisTemplate.opsForHash().get("user", openId);
        User user = (User) o;

        String name = user.getDisplayName();
        String avatar = user.getAvatarUrl();
        Room room = roomService.joinRoom(code, openId, name, avatar);
        return ResponseEntity.ok(Map.of("code", room.getCode()));
    }

    @PostMapping("/{code}/leave")
    public ResponseEntity<?> leave(HttpServletRequest request, @PathVariable String code) {
        String openId = getOpenId(request);
        roomService.leaveRoom(code, openId);
        return ResponseEntity.ok().build();
    }

    @PostMapping("/{code}/kick")
    public ResponseEntity<?> kick(HttpServletRequest request, @PathVariable String code, @RequestBody Map<String, String> body) {
        String openId = getOpenId(request);
        String target = body.get("targetOpenId");
        roomService.kickMember(code, openId, target);
        return ResponseEntity.ok().build();
    }

    @PostMapping("/{code}/dissolve")
    public ResponseEntity<?> dissolve(HttpServletRequest request, @PathVariable String code) {
        String openId = getOpenId(request);
        roomService.dissolveRoom(code, openId);
        return ResponseEntity.ok().build();
    }

    @GetMapping("/{code}/members")
    public List<RoomMember> members(@PathVariable String code) {
        return roomService.getMembers(code);
    }

    @PostMapping("/{code}/name")
    public ResponseEntity<?> updateName(HttpServletRequest request, @PathVariable String code, @RequestBody Map<String, String> body) {
        String openId = getOpenId(request);
        String name = body.get("name");
        roomService.updateDisplayName(code, openId, name);
        return ResponseEntity.ok().build();
    }

    @PostMapping("/{code}/transfer")
    public ResponseEntity<?> transfer(HttpServletRequest request, @PathVariable String code, @RequestBody Map<String, Object> body) {
        String fromOpenId = getOpenId(request);
        String toOpenId = (String) body.get("toOpenId");
        int amount = (int) body.getOrDefault("amount", 0);
        roomService.transferScore(code, fromOpenId, toOpenId, amount);
        return ResponseEntity.ok().build();
    }


    @GetMapping("/{code}/transactions")
    public List<ScoreTransaction> transactions(@PathVariable String code) {
        return roomService.getRoomTransactions(code);
    }

    @GetMapping("/current/{openId}")
    public ResponseEntity<?> current(@PathVariable String openId) {
        Map<String, Object> result = new HashMap<>();
        Object o = redisTemplate.opsForHash().get("user", openId);
        if (o != null) {
            result.put("nickName", ((User) o).getDisplayName());
        }
        Map<String, Object> currentRoom = roomService.getCurrentRoom(openId);
        if (currentRoom == null) {
            return ResponseEntity.ok(result);
        } else {
            BeanUtil.beanToMap(currentRoom, result, null);
            return ResponseEntity.ok(result);
        }
    }

    @GetMapping("/info/{code}/{openId}")
    public ResponseEntity<?> roomInfo(@PathVariable String code, @PathVariable String openId) {
        Map<String, Object> result = new HashMap<>();
        // 房间信息
        Room room = roomService.findByCode(code);
        if (room == null) {
            result.put("room", new HashMap<>());
        } else {
            Map<String, Object> m = new HashMap<>();
            m.put("code", code);
            m.put("ownerOpenId", room.getOwnerOpenId());
            result.put("room", m);
        }
        // 成员信息
        result.put("members", roomService.getMembers(code));
        // 交易记录
        result.put("transactions", roomService.getRoomTransactions(code));
        return ResponseEntity.ok(result);
    }
}

