-- MySQL DDL for Mahjong app
CREATE TABLE rooms (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  code VARCHAR(6) NOT NULL UNIQUE,
  owner_open_id VARCHAR(64),
  created_at TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP,
  last_active_at TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE room_members (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  room_id BIGINT,
  open_id VARCHAR(64),
  display_name VARCHAR(64),
  avatar_url VARCHAR(255),
  balance INT DEFAULT 0,
  CONSTRAINT fk_member_room FOREIGN KEY (room_id) REFERENCES rooms(id) ON DELETE CASCADE,
  INDEX idx_room_member (room_id, open_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE score_transactions (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  room_id BIGINT,
  from_open_id VARCHAR(64),
  to_open_id VARCHAR(64),
  from_name VARCHAR(64),
  to_name VARCHAR(64),
  amount INT NOT NULL,
  created_at TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP,
  CONSTRAINT fk_tx_room FOREIGN KEY (room_id) REFERENCES rooms(id) ON DELETE CASCADE,
  INDEX idx_user_time (from_open_id, created_at),
  INDEX idx_user_time2 (to_open_id, created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


